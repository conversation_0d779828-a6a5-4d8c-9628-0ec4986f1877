import React, { Suspense } from 'react'
import ImageScalerComponent from '@/components/ImageScalerComponent';
import Navbar from '@/components/Navbar';

export default function page() {
  return (
    <main className="flex w-full h-full items-center justify-center overflow-hidden">
      {/* <ImageScalerComponent 
        style={'object-cover md:w-full w-full h-auto'} 
        src={'/assets/website_under_construction_002.jpg'} 
        alt='landingpage backround image'
      /> */}
      <img 
        className={'object-cover md:w-full w-auto h-full'} 
        src={'/assets/website_under_construction_002.jpg'} 
        alt='landingpage backround image'
      />
      <Suspense fallback={null}>
      </Suspense>
    </main>
  )
}
