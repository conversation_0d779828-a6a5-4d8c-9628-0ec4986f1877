import mongoose from 'mongoose';
const { Schema } = mongoose;

// Testimonial schema for testimonials section
const TestimonialSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: true,
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  }
}, { _id: true });

// Additional content item schema for island and experiences pages
const AdditionalContentItemSchema = new Schema({
  image: {
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return false; // Image is required
        return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body1: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body1 cannot exceed 5000 characters']
  }
}, { _id: false });

// Island page schema
const IslandPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  image: {
    type: String,
    required: false,
    default: '',
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Allow empty values
        return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  body1: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body1 cannot exceed 5000 characters']
  },
  additionalContent: {
    type: [AdditionalContentItemSchema],
    default: [],
    validate: {
      validator: function(v) {
        return v.length <= 10; // Maximum 10 additional content items
      },
      message: 'Cannot have more than 10 additional content items'
    }
  }
}, { _id: false });

// Experiences page schema
const ExperiencesPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  image: {
    type: String,
    required: false,
    default: '',
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Allow empty values
        return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  body1: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body1 cannot exceed 5000 characters']
  },
  additionalContent: {
    type: [AdditionalContentItemSchema],
    default: [],
    validate: {
      validator: function(v) {
        return v.length <= 10; // Maximum 10 additional content items
      },
      message: 'Cannot have more than 10 additional content items'
    }
  }
}, { _id: false });

// Testimonials page schema
const TestimonialsPageSchema = new Schema({
  testimonials: {
    type: [TestimonialSchema],
    default: [],
    validate: [
      {
        validator: function(v) {
          return v.length <= 20; // Maximum 20 testimonials
        },
        message: 'Cannot have more than 20 testimonials'
      },
      {
        validator: function(v) {
          // Check for unique names within testimonials
          const names = v.map(t => t.name.toLowerCase());
          return names.length === new Set(names).size;
        },
        message: 'Testimonial names must be unique'
      }
    ]
  }
}, { _id: false });

// Location and contacts page schema
const LocationAndContactsPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body cannot exceed 5000 characters']
  },
  details: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Details cannot exceed 5000 characters']
  }
}, { _id: false });

// Booking page schema
const BookingPageSchema = new Schema({
  details: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Details cannot exceed 5000 characters']
  }
}, { _id: false });

// Main Page schema with 5 object fields
const PageSchema = new Schema({
  island: {
    type: IslandPageSchema,
    default: null
  },
  experiences: {
    type: ExperiencesPageSchema,
    default: null
  },
  testimonials: {
    type: TestimonialsPageSchema,
    default: null
  },
  locationAndcontacts: {
    type: LocationAndContactsPageSchema,
    default: null
  },
  booking: {
    type: BookingPageSchema,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
PageSchema.index({ createdAt: -1 });

// Static method to initialize default pages
PageSchema.statics.initializeDefaultPages = async function() {
  const existingPage = await this.findOne();
  if (!existingPage) {
    const defaultData = {
      island: {
        title: 'Welcome to The Island',
        image: '',
        body1: 'Discover the beauty and wonder of our pristine island paradise.',
        additionalContent: []
      },
      experiences: {
        title: 'Unforgettable Experiences',
        image: '',
        body1: 'Create memories that will last a lifetime with our curated experiences.',
        additionalContent: []
      },
      testimonials: {
        testimonials: []
      },
      locationAndcontacts: {
        title: 'Contact Information',
        body: 'Get in touch with us for more information about Elephant Island Lodge.',
        details: 'Additional contact details and information about our location and services.'
      },
      booking: {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      }
    };

    await this.create(defaultData);
  }
};

// Pre-save middleware for data validation and cleanup
PageSchema.pre('save', function(next) {
  // Clean up empty testimonials
  if (this.testimonials && this.testimonials.testimonials) {
    this.testimonials.testimonials = this.testimonials.testimonials.filter(testimonial =>
      testimonial.name && testimonial.comment
    );
  }

  // Clean up empty additional content items for island and remove deprecated body2 fields
  if (this.island && this.island.additionalContent) {
    this.island.additionalContent = this.island.additionalContent
      .filter(item => item.image && item.title && item.body1)
      .map(item => {
        // Remove deprecated body2 field if it exists
        const cleanItem = {
          image: item.image,
          title: item.title,
          body1: item.body1
        };
        return cleanItem;
      });
  }

  // Clean up empty additional content items for experiences and remove deprecated body2 fields
  if (this.experiences && this.experiences.additionalContent) {
    this.experiences.additionalContent = this.experiences.additionalContent
      .filter(item => item.image && item.title && item.body1)
      .map(item => {
        // Remove deprecated body2 field if it exists
        const cleanItem = {
          image: item.image,
          title: item.title,
          body1: item.body1
        };
        return cleanItem;
      });
  }

  next();
});

// Static method to migrate existing data and remove deprecated body2 fields
PageSchema.statics.migrateDeprecatedFields = async function() {
  try {
    const pages = await this.find({});

    for (const page of pages) {
      let needsUpdate = false;

      // Check and clean island additional content
      if (page.island && page.island.additionalContent) {
        const originalLength = page.island.additionalContent.length;
        page.island.additionalContent = page.island.additionalContent.map(item => {
          if (item.body2 !== undefined) {
            needsUpdate = true;
            // Remove body2 field
            const { body2, ...cleanItem } = item.toObject ? item.toObject() : item;
            return cleanItem;
          }
          return item;
        });
      }

      // Check and clean experiences additional content
      if (page.experiences && page.experiences.additionalContent) {
        page.experiences.additionalContent = page.experiences.additionalContent.map(item => {
          if (item.body2 !== undefined) {
            needsUpdate = true;
            // Remove body2 field
            const { body2, ...cleanItem } = item.toObject ? item.toObject() : item;
            return cleanItem;
          }
          return item;
        });
      }

      if (needsUpdate) {
        await page.save();
        console.log('Migrated page data to remove deprecated body2 fields');
      }
    }
  } catch (error) {
    console.error('Error migrating deprecated fields:', error);
  }
};

// Use existing model if it exists, otherwise create a new one
export const Page = mongoose.models.Page || mongoose.model('Page', PageSchema);
