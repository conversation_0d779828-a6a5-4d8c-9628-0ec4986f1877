export const INITIAL_EXPERIENCE_STATE = {
    showNavbar: false,
    showMenu: false,
    showPopup: false,
    showBookingPopup: false,
    showGalleryStore: false,
    showVideoGallery: false,
    showSingleVideoGallery: false,
    showItemInfo: {},
    showVideoInfo: {},
    showTheIslandPage:false,
    showExperiencePage:false,
    showTestimonials:false,
    showLocationAndContacts:false,
};

export const ACTIONS_EXPERIENCE_STATE = {
    SHOW_NAVBAR: 'SHOW_NAVBAR',
    SHOW_ISLAND_PAGE: 'SHOW_ISLAND_PAGE',
    SHOW_EXPERIENCE_PAGE: 'SHOW_EXPERIENCE_PAGE',
    SHOW_TESTIMONIALS_PAGE: 'SHOW_TESTIMONIALS_PAGE',
    SHOW_LOCATION_AND_CONTACTS_PAGE: 'SHOW_LOCATION_AND_CONTACTS_PAGE',
    MENU_TOGGLE: 'MENU_TOGGLE',
    POPUP_TOGGLE: 'POPUP_TOGGLE',
    POPUP_BOOKING_TOGGLE: 'POPUP_BOOKING_TOGGLE',
    POPUP_STORE_TOGGLE: 'POPUP_STORE_TOGGLE',
    POPUP_VIDOE_GALLERY_TOGGLE: 'POPUP_VIDOE_GALLERY_TOGGLE',
    POPUP_ITEM_ARTICLE_TOGGLE: 'POPUP_ITEM_ARTICLE_TOGGLE',
    POPUP_SINGLE_VIDOE_GALLERY_TOGGLE: 'POPUP_SINGLE_VIDOE_GALLERY_TOGGLE',
    CLOSE_PAGES: 'CLOSE_PAGES',
    RESET: 'RESET',
};

export const experienceReducer = (state, action) => {
    switch (action.type) {
      case 'SHOW_NAVBAR':
        return { 
          ...state, 
          showNavbar: !state.showNavbar,
        };
      case 'SHOW_ISLAND_PAGE':
        return { 
          ...state, 
          showTheIslandPage: !state.showTheIslandPage,
          showExperiencePage:false,
          showTestimonials:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_EXPERIENCE_PAGE':
        return { 
          ...state, 
          showExperiencePage: !state.showExperiencePage,
          showTheIslandPage:false,
          showTestimonials:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_TESTIMONIALS_PAGE':
        return { 
          ...state, 
          showTestimonials: !state.showTestimonials,
          showTheIslandPage:false,
          showExperiencePage:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_LOCATION_AND_CONTACTS_PAGE':
        return { 
          ...state, 
          showLocationAndContacts: !state.showLocationAndContacts,
          showTheIslandPage:false,
          showExperiencePage:false,
          showTestimonials:false, 
        };
      case 'CLOSE_PAGES':
        return { 
          ...state, 
          showTheIslandPage:false,
          showExperiencePage:false,
          showTestimonials:false,
          showLocationAndContacts:false, 
        };
      case 'MENU_TOGGLE':
        return { 
          ...state, 
          showMenu: !state.showMenu,
          showPopup:false
        };
      case 'POPUP_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup  
        };
      case 'POPUP_BOOKING_TOGGLE':
        return { 
          ...state, 
          // showBookingPopup: true,
          showBookingPopup: !state.showBookingPopup,
        };
      case 'POPUP_STORE_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showGalleryStore: true,
          showVideoGallery: false,
          showSingleVideoGallery: false,   
          showItemInfo: false, 
          showMenu:false   
        };
      case 'POPUP_VIDOE_GALLERY_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showVideoGallery: true,
          showGalleryStore: false,
          showItemInfo: false, 
          showSingleVideoGallery: false,   
          showMenu:false
        };
      case 'POPUP_SINGLE_VIDOE_GALLERY_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showVideoGallery: false,
          showGalleryStore: false,
          showItemInfo: false, 
          showSingleVideoGallery: true, 
          showVideoInfo: action.payload,
          showMenu:false
        };
      case 'POPUP_ITEM_ARTICLE_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showItemInfo: {showItem:true,id:action.payload},
          showGalleryStore: false,
          showVideoGallery: false,  
          showSingleVideoGallery: false,   
          showMenu:false  
        };
      case 'RESET':
        return { 
          ...state, 
          showNavbar: false,
          showMenu: false,
          showPopup: false,
          showBookingPopup: false,
          showGalleryStore: false,
          showVideoGallery: false,
          showSingleVideoGallery: false,
          showItemInfo: false, 
          showVideoInfo: false,
        };
      default:
        return state;
    }
  };