'use client'

import { createContext, useContext, useReducer } from "react"
import { experienceReducer,INITIAL_EXPERIENCE_STATE } from "./reducerExperience"

const ExperienceContext=createContext()

export function ExperienceContextProvider({children}) {
    const [experienceState,disptachExperience]=useReducer(experienceReducer,INITIAL_EXPERIENCE_STATE)

  return <ExperienceContext.Provider value={{
        experienceState,disptachExperience
    }}>
        {children}
    </ExperienceContext.Provider>
}

export function useContextExperience(){
    const context=useContext(ExperienceContext)
    if(!context){
        throw new Error('useExperienceContext must be used within a ExperienceContextProvider');
    }
    return context
}
