'use client'
import React, { useEffect } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { settings } from '@/lib/settings'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import Image from 'next/image'
import Link from 'next/link'

function BtnLandingpageComponent({data,link}) {
    const [swap,setSwap]=useState(true)
    const {disptachExperience}=useContextExperience()
    console.log('BtnLandingpageComponent links:',link)
    console.log('BtnLandingpageComponent data:',data)
    return(
      <Link
        href={data?.name==='home' ? '/beta' : `/360s?id=${link}`} 
        onMouseEnter={()=>setSwap(!swap)} 
        onMouseLeave={()=>setSwap(!swap)} 
        onClick={()=>disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})}
        className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
      >
        <div 
          className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
        >
          <ImageWrapperResponsive className='flex-none' alt='button images for landpage options' src={data?.btnIcons?.ov}/>
        </div>
        <div
            className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
        >
          <ImageWrapperResponsive className='flex-none' alt='button images for landpage options' src={data?.btnIcons?.off}/>
        </div>
      </Link>
    )
}

export default function   MenuWrapper() {
  const lineClass2='w-full border-1 border-gray-400/30 '
  const lineClass='w-full border-1 mt-2 border-gray-400/30'
  const [menuLinks,setMenuLinks]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData =async () => {
    try {
      const res=await fetch(`${settings.url}/api/site-management`)
      const data=await res.json()
      if(!data) return
      // console.log(data)
      // const {menuLinks}=data
      data && setMenuLinks(data?.data?.menulinks)
      // setMenuLinks(menuLinks)
    } catch (error) {
      console.log(error.message);
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  console.log('MenuWrapper:',menuLinks)

  return (
    (experienceState?.showMenu && <div className='flex flex-col w-full top-0 left-0 h-[calc(100%-80px)] overflow-y-auto'>
      <div className='w-80'>
        <hr className={lineClass2}/>
        <div className='flex w-full h-fit'>
          {settings.menuPopup.home.map((i,index)=>
            <BtnLandingpageComponent key={index} data={i}/>
          )}
        </div>
        <hr className={lineClass2}/>
        {settings.menuPopup.entrance.map((i,index)=>
          <BtnLandingpageComponent key={index} data={i} link={menuLinks?.entrance[index]}/>
        )}
        <hr className={lineClass}/>
        {settings.menuPopup.firstFloor.map((i,index)=>
          <BtnLandingpageComponent key={index} data={i} link={menuLinks?.firstFloor[index]}/>
        )}
        <hr className={lineClass}/>
        {settings.menuPopup.outDoors.map((i,index)=>
          <BtnLandingpageComponent key={index} data={i} link={menuLinks?.outDoors[index]}/>
        )}
        <hr className={lineClass}/>
        {settings.menuPopup.campOutskirts.map((i,index)=>
          <BtnLandingpageComponent key={index} data={i} link={menuLinks?.campOutskirts[index]}/>
        )}
        <hr className={lineClass}/>
      </div>
    </div>)
  )
}
