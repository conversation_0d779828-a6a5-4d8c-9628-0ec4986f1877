'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { applyReactDomPolyfill } from '../../lib/reactDomPolyfill';

// Comprehensive suppression of ReactQuill deprecation warnings
if (typeof window !== 'undefined') {
  // Store original methods if not already stored
  if (!window.__quillWarningsSuppressed) {
    window.__quillWarningsSuppressed = true;

    // Store original console methods
    window.__originalConsoleWarn = console.warn;
    window.__originalConsoleError = console.error;

    // Override console.warn to filter out ReactQuill deprecation warnings
    console.warn = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation warnings
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('ReactQuillWrapper.jsx') ||
        message.includes('Listener added for a \'DOMNodeInserted\'') ||
        message.includes('Support for this event type has been removed') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these warnings
      }

      // Call original console.warn for other warnings
      window.__originalConsoleWarn.apply(console, args);
    };

    // Override console.error to filter out ReactQuill deprecation errors
    console.error = (...args) => {
      const message = args.join(' ');

      // Filter out specific ReactQuill/Quill.js deprecation errors
      if (
        message.includes('DOMNodeInserted') ||
        message.includes('DOMNodeRemoved') ||
        message.includes('DOMSubtreeModified') ||
        message.includes('mutation event') ||
        message.includes('[Deprecation]') ||
        message.includes('deprecated') ||
        message.includes('will no longer be fired')
      ) {
        return; // Suppress these errors
      }

      // Call original console.error for other errors
      window.__originalConsoleError.apply(console, args);
    };

    // Override addEventListener to suppress mutation event warnings at the source
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // Suppress mutation event listeners that trigger deprecation warnings
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalAddEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Also override removeEventListener for completeness
    const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
    EventTarget.prototype.removeEventListener = function(type, listener, options) {
      if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
        // Temporarily suppress console warnings during this call
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = () => {};
        console.error = () => {};

        try {
          const result = originalRemoveEventListener.call(this, type, listener, options);
          return result;
        } finally {
          // Restore console methods
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalRemoveEventListener.call(this, type, listener, options);
    };
  }
}



// Simple fallback component that doesn't use ReactQuill
const FallbackEditor = ({ value, onChange, placeholder, className, style, ...props }) => (
  <textarea
    className={`w-full p-3 border border-gray-300 rounded-md resize-y ${className || ''}`}
    value={value || ''}
    onChange={(e) => onChange && onChange(e.target.value)}
    placeholder={placeholder || 'Rich text editor (using fallback textarea)'}
    style={{ minHeight: '120px', ...style }}
    rows={6}
    {...props}
  />
);

// Dynamically import ReactQuill with comprehensive error handling
const ReactQuill = dynamic(
  () => {
    // Return the fallback component for now to avoid React 19 compatibility issues
    return Promise.resolve(FallbackEditor);

    // TODO: Re-enable ReactQuill when React 19 compatibility is resolved
    /*
    return import('react-quill').then(mod => {
      return mod.default;
    }).catch(error => {
      console.error('Error loading ReactQuill:', error);
      return FallbackEditor;
    });
    */
  },
  {
    ssr: false,
    loading: () => (
      <div className="border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }
);


export default function ReactQuillWrapper({
  value,
  onChange,
  placeholder,
  modules,
  formats,
  theme = 'snow',
  style,
  className,
  ...props
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={`border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 ${className || ''}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="react-quill-wrapper">
      <ReactQuill
        theme={theme}
        value={value || ''}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        style={style}
        className={className}
        {...props}
      />
    </div>
  );
}
