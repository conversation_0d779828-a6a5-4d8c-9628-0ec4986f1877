'use client'
import { Html, useIntersect } from '@react-three/drei'
import React, { useState, useRef, useMemo, useCallback, memo, useEffect } from 'react'
import Link from 'next/link'
import ImageScalerComponent from '../ImageScalerComponent'
import { settings } from '@/lib/settings'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useFrame, useThree } from '@react-three/fiber'
import * as THREE from 'three'

// Memoized IconGuides component for better performance
const IconGuides = memo(({ item, icon, experienceState, disptachExperience }) => {
  const [onHover, setOnHover] = useState(false)
  console.log('IconGuides',item)
  // const { disptachExperience } = useContextExperience()

  // Memoized event handlers
  const handleMouseEnter = useCallback(() => setOnHover(true), [])
  const handleMouseLeave = useCallback(() => setOnHover(false), [])

  // Memoized href to prevent unnecessary re-renders
  const href = useMemo(() => `/360s?id=${item?._360Name}`, [item?._360Name])

  // function for the markers
  const handleClick = () => {
    item?.markerType=='infoDoc' && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_ITEM_ARTICLE_TOGGLE, payload:item?.id})
    item?.markerType==='infoVideo' && item?.infoVideo?.title?.lenght>0 ? disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_SINGLE_VIDOE_GALLERY_TOGGLE,payload:item?.infoVideo}) : disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_VIDOE_GALLERY_TOGGLE})
    item?.markerType==='infoImage' && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_STORE_TOGGLE})
  }
  

  // Memoized icon sources
  const iconSrc = useMemo(() => ({
    off: icon?.btnIcons?.off,
    on: icon?.btnIcons?.ov
  }), [icon?.btnIcons?.off, icon?.btnIcons?.ov])

  return (
    <>
      { item?.markerType === 'infoVideo' || item?.markerType === 'infoDoc' || item?.markerType === 'infoImage'
        ? <div
            onClick={() => {
              // TODO: Handle content marker clicks (open modal, show content, etc.)
              // disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_TOGGLE,payload:item?.id})
              // console.log('Content marker clicked:', item?.id);
              handleClick()
            }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
          >
            <ImageScalerComponent
              src={onHover ? iconSrc.on : iconSrc.off}
              alt="marker icon"
            />
          </div>
        : <Link
            href={href}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
          >
            <ImageScalerComponent
              src={onHover ? iconSrc.on : iconSrc.off}
              alt="marker icon"
            />
          </Link>
      }
    </>
  )
})

// Optimized MarkerPosition component with proper positioning and visibility
const MarkerPosition = memo(({ position, children }) => {
  const { camera } = useThree()
  const refGroup = useRef(null)
  const [markerVisible, setMarkerVisible] = useState(false)
  const [isPositioned, setIsPositioned] = useState(false)
  const [isInFrustum, setIsInFrustum] = useState(false)

  // Memoized frustum and matrix for performance
  const frustum = useMemo(() => new THREE.Frustum(), [])
  const matrix = useMemo(() => new THREE.Matrix4(), [])

  // Memoized position values with fallbacks and validation
  const positionArray = useMemo(() => {
    const x = typeof position.x === 'number' && !isNaN(position.x) ? position.x : 0
    const y = typeof position.y === 'number' && !isNaN(position.y) ? position.y : 0
    const z = typeof position.z === 'number' && !isNaN(position.z) ? position.z : 0

    return [x, y, z]
  }, [position.x, position.y, position.z])

  // Memoized position vector for frustum culling
  const positionVector = useMemo(() =>
    new THREE.Vector3(...positionArray),
    [positionArray]
  )

  // Set positioned state when we have valid coordinates
  useEffect(() => {
    const hasValidPosition = positionArray[0] !== 0 || positionArray[1] !== 0 || positionArray[2] !== 0
    setIsPositioned(hasValidPosition)
  }, [positionArray])

  // Intersection observer for basic visibility
  useIntersect(refGroup, (isVisible) => {
    setMarkerVisible(isVisible);
  });

  // Optimized frustum culling with proper camera checks
  useFrame(() => {
    if (camera && positionVector && isPositioned) {
      matrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse)
      frustum.setFromProjectionMatrix(matrix)

      const inFrustum = frustum.containsPoint(positionVector)
      if (inFrustum !== isInFrustum) {
        setIsInFrustum(inFrustum)
      }
    }
  })

  // Only render if positioned and either visible or in frustum
  const shouldRender = isPositioned && (markerVisible || isInFrustum)

  return (
    <group
      visible={shouldRender}
      ref={refGroup}
      position={positionArray}
    >
      {shouldRender && children}
    </group>
  )
})

// Optimized MarkerIcon component with smooth positioning
const MarkerIcon = memo(({ item, disptachExperience, experienceState }) => {
  const [onHover, setOnHover] = useState(false)
  const [isReady, setIsReady] = useState(false)

  // Memoized safe item to prevent unnecessary re-renders
  const safeItem = useMemo(() => item || {}, [item])

  // Memoized position calculation with validation and immediate positioning
  const position = useMemo(() => {
    const x = isNaN(parseFloat(item?.x)) ? 0 : parseFloat(item.x)
    const y = isNaN(parseFloat(item?.y)) ? 0 : parseFloat(item.y)
    const z = isNaN(parseFloat(item?.z)) ? 0 : parseFloat(item.z)

    return { x, y, z }
  }, [item?.x, item?.y, item?.z])

  // Set ready state immediately when we have valid position data
  useEffect(() => {
    const hasValidPosition = position.x !== 0 || position.y !== 0 || position.z !== 0
    if (hasValidPosition) {
      setIsReady(true)
    } else {
      setIsReady(false)
    }
  }, [position])

  // Memoized event handler
  const handleEntranceClick = useCallback(() => {
    // console.log('entrance clicked')
    disptachExperience({ type: ACTIONS_EXPERIENCE_STATE.SHOW_NAVBAR })
  }, [disptachExperience])

  // console.log(experienceState)

  // Memoized hover handlers
  const handlePointerOver = useCallback(() => setOnHover(true), [])
  const handlePointerOut = useCallback(() => setOnHover(false), [])

  // Memoized marker content based on type
  const markerContent = useMemo(() => {
    const markerType = safeItem.markerType;
    const iconSettings = settings.markerList.markerTypeIcons;

    switch (markerType) {
      case 'landingPage':
        return (
          <Link 
            onClick={handleEntranceClick} 
            href={`/360s?id=entrance_360`}
          >
            {iconSettings.landingPage.btnIcons.off}
          </Link>
        );
      case 'guide':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.guide} />;
      case 'upstairs':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.upstairs} />;
      case 'downstairs':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.downstairs} />;
      case 'infoVideo':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.infoVideo} />;
      case 'infoDoc':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.infoDoc} />;
      case 'infoImage':
        return <IconGuides item={item} experienceState={experienceState} disptachExperience={disptachExperience} icon={iconSettings.infoImage} />;
      default:
        return null;
    }
  }, [safeItem.markerType, item, handleEntranceClick]);

  // Only render when marker is ready and has valid position
  if (!isReady || (position.x === 0 && position.y === 0 && position.z === 0)) {
    return null
  }

  return (
    <MarkerPosition position={position}>
      <Html 
        prepend
        center
        zIndexRange={[1, 10]}
        style={{
          transition: 'all 0.30s ease-in-out',
          opacity: isReady ? 1 : 0,
        }}
      >
        <div
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          className='flex w-fit cursor-pointer h-fit items-center justify-center'
        >
          {markerContent}
        </div>
      </Html>
    </MarkerPosition>
  )
})

// Optimized main component with memoization and proper image ID handling
function _360InfoMarkers({ markerList, disptachExperience, experienceState, currentImageId }) {
  // Memoized safe marker list to prevent unnecessary re-renders
  const safeMarkerList = useMemo(() =>
    Array.isArray(markerList) ? markerList : [],
    [markerList]
  )

  // Create a stable key that includes currentImageId to force re-render on image change
  const componentKey = useMemo(() => {
    return `markers-${currentImageId || 'no-id'}-${safeMarkerList.length}`;
  }, [currentImageId, safeMarkerList.length]);

  // Memoized marker components to prevent recreation with proper keys for image changes
  const markerComponents = useMemo(() =>
    safeMarkerList.map((item, index) => (
      <MarkerIcon
        disptachExperience={disptachExperience}
        experienceState={experienceState}
        key={`${currentImageId}-marker-${item?.name || item?.id || 'unnamed'}-${index}-${item?.markerType || 'no-type'}`}
        item={item}
      />
    )),
    [safeMarkerList, currentImageId, disptachExperience, experienceState]
  )

  return <group key={componentKey}>{markerComponents}</group>
}

// Export memoized component for better performance
export default memo(_360InfoMarkers)
