/* ReactQuill deprecation warning fixes and optimizations */

/* Hide any console warning overlays that might appear */
.react-quill-wrapper {
  position: relative;
}

/* Ensure Quill editor renders properly without mutation observer issues */
.ql-editor {
  min-height: 80px;
  font-family: inherit;
}

/* Fix for any layout issues caused by deprecated mutation events */
.ql-container {
  font-family: inherit;
}

/* Prevent any flash of unstyled content during loading */
.ql-snow {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.ql-snow .ql-toolbar {
  border-bottom: 1px solid #d1d5db;
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0.375rem 0.375rem 0 0;
}

.ql-snow .ql-container {
  border-top: none;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-radius: 0 0 0.375rem 0.375rem;
}

/* Improve focus states */
.ql-snow.ql-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Loading state improvements */
.react-quill-loading {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.75rem;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Error state styling */
.react-quill-error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* Suppress any browser deprecation warning popups */
.ql-editor::before {
  content: none !important;
}

/* Ensure proper z-index for dropdowns */
.ql-snow .ql-picker-options {
  z-index: 1000;
}

/* Font size support - Global styles for all contexts */
.ql-size-small {
  font-size: 0.75em !important;
}

.ql-size-large {
  font-size: 1.5em !important;
}

.ql-size-huge {
  font-size: 2.5em !important;
}

/* Ensure font sizes are preserved in editor content */
.ql-editor .ql-size-small {
  font-size: 0.75em !important;
}

.ql-editor .ql-size-large {
  font-size: 1.5em !important;
}

.ql-editor .ql-size-huge {
  font-size: 2.5em !important;
}

/* Font size support for display contexts (HtmlContentDisplay) */
.html-content-display .ql-size-small,
.quill-content .ql-size-small {
  font-size: 0.75em !important;
}

.html-content-display .ql-size-large,
.quill-content .ql-size-large {
  font-size: 1.5em !important;
}

.html-content-display .ql-size-huge,
.quill-content .ql-size-huge {
  font-size: 2.5em !important;
}

/* Ensure nested elements inherit font sizes properly */
.html-content-display span[class*="ql-size"],
.quill-content span[class*="ql-size"] {
  display: inline;
  line-height: inherit;
}

/* Support for paragraphs and other block elements with font sizes */
.html-content-display p .ql-size-small,
.html-content-display div .ql-size-small,
.quill-content p .ql-size-small,
.quill-content div .ql-size-small {
  font-size: 0.75em !important;
}

.html-content-display p .ql-size-large,
.html-content-display div .ql-size-large,
.quill-content p .ql-size-large,
.quill-content div .ql-size-large {
  font-size: 1.5em !important;
}

.html-content-display p .ql-size-huge,
.html-content-display div .ql-size-huge,
.quill-content p .ql-size-huge,
.quill-content div .ql-size-huge {
  font-size: 2.5em !important;
}

/* Enhanced support for BookingFormComponent context (black background, white text) */
.text-white .html-content-display .ql-size-small,
.text-white .quill-content .ql-size-small,
.booing-form .ql-size-small {
  font-size: 0.75em !important;
  color: inherit;
}

.text-white .html-content-display .ql-size-large,
.text-white .quill-content .ql-size-large,
.booing-form .ql-size-large {
  font-size: 1.5em !important;
  color: inherit;
}

.text-white .html-content-display .ql-size-huge,
.text-white .quill-content .ql-size-huge,
.booing-form .ql-size-huge {
  font-size: 2.5em !important;
  color: inherit;
}

/* Support for deeply nested elements in booking context */
.text-white .html-content-display p .ql-size-small,
.text-white .html-content-display div .ql-size-small,
.text-white .html-content-display span .ql-size-small,
.booing-form p .ql-size-small,
.booing-form div .ql-size-small,
.booing-form span .ql-size-small {
  font-size: 0.75em !important;
  color: inherit;
}

.text-white .html-content-display p .ql-size-large,
.text-white .html-content-display div .ql-size-large,
.text-white .html-content-display span .ql-size-large,
.booing-form p .ql-size-large,
.booing-form div .ql-size-large,
.booing-form span .ql-size-large {
  font-size: 1.5em !important;
  color: inherit;
}

.text-white .html-content-display p .ql-size-huge,
.text-white .html-content-display div .ql-size-huge,
.text-white .html-content-display span .ql-size-huge,
.booing-form p .ql-size-huge,
.booing-form div .ql-size-huge,
.booing-form span .ql-size-huge {
  font-size: 2.5em !important;
  color: inherit;
}
