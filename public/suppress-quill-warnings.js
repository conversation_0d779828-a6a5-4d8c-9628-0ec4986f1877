// Global script to suppress ReactQuill deprecation warnings
// This runs before any React components are loaded

(function() {
  'use strict';
  
  // Only run in browser environment
  if (typeof window === 'undefined') return;
  
  // Prevent multiple executions
  if (window.__quillWarningsGloballySuppressed) return;
  window.__quillWarningsGloballySuppressed = true;
  
  // Store original console methods
  const originalWarn = console.warn;
  const originalError = console.error;
  
  // Function to check if message should be suppressed
  function shouldSuppressMessage(message) {
    return (
      message.includes('DOMNodeInserted') ||
      message.includes('DOMNodeRemoved') ||
      message.includes('DOMSubtreeModified') ||
      message.includes('mutation event') ||
      message.includes('Listener added for a \'DOMNodeInserted\'') ||
      message.includes('Support for this event type has been removed') ||
      message.includes('[Deprecation]') ||
      message.includes('deprecated') ||
      message.includes('will no longer be fired') ||
      message.includes('ReactQuillWrapper.jsx') ||
      message.includes('quill') ||
      message.includes('Quill')
    );
  }
  
  // Override console.warn
  console.warn = function(...args) {
    const message = args.join(' ');
    if (shouldSuppressMessage(message)) {
      return; // Suppress the warning
    }
    originalWarn.apply(console, args);
  };
  
  // Override console.error
  console.error = function(...args) {
    const message = args.join(' ');
    if (shouldSuppressMessage(message)) {
      return; // Suppress the error
    }
    originalError.apply(console, args);
  };
  
  // Store original EventTarget methods
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
  
  // Override addEventListener to suppress warnings at the source
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
      // Temporarily disable console methods during this call
      const tempWarn = console.warn;
      const tempError = console.error;
      console.warn = function() {};
      console.error = function() {};
      
      try {
        return originalAddEventListener.call(this, type, listener, options);
      } finally {
        // Restore console methods
        console.warn = tempWarn;
        console.error = tempError;
      }
    }
    return originalAddEventListener.call(this, type, listener, options);
  };
  
  // Override removeEventListener for completeness
  EventTarget.prototype.removeEventListener = function(type, listener, options) {
    if (type === 'DOMNodeInserted' || type === 'DOMNodeRemoved' || type === 'DOMSubtreeModified') {
      // Temporarily disable console methods during this call
      const tempWarn = console.warn;
      const tempError = console.error;
      console.warn = function() {};
      console.error = function() {};
      
      try {
        return originalRemoveEventListener.call(this, type, listener, options);
      } finally {
        // Restore console methods
        console.warn = tempWarn;
        console.error = tempError;
      }
    }
    return originalRemoveEventListener.call(this, type, listener, options);
  };
  
  // Also intercept any direct calls to deprecated mutation events
  const originalCreateEvent = document.createEvent;
  if (originalCreateEvent) {
    document.createEvent = function(eventType) {
      if (eventType === 'MutationEvents' || eventType === 'MutationEvent') {
        // Suppress warnings for mutation event creation
        const tempWarn = console.warn;
        const tempError = console.error;
        console.warn = function() {};
        console.error = function() {};
        
        try {
          return originalCreateEvent.call(this, eventType);
        } finally {
          console.warn = tempWarn;
          console.error = tempError;
        }
      }
      return originalCreateEvent.call(this, eventType);
    };
  }
  
  // Store references for potential cleanup
  window.__quillWarningSuppressionCleanup = function() {
    console.warn = originalWarn;
    console.error = originalError;
    EventTarget.prototype.addEventListener = originalAddEventListener;
    EventTarget.prototype.removeEventListener = originalRemoveEventListener;
    if (originalCreateEvent) {
      document.createEvent = originalCreateEvent;
    }
    window.__quillWarningsGloballySuppressed = false;
  };
  
  console.log('ReactQuill deprecation warning suppression activated');
})();
